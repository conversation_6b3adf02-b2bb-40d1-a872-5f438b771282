#!/usr/bin/env python3
"""
Test script for the new /intervet_new endpoint
"""

import requests
import json
import time

# Test data
resume_data = {
    "name": "<PERSON>",
    "email": "<EMAIL>",
    "skills": ["Python", "JavaScript", "SQL", "Machine Learning", "FastAPI"],
    "experience": [
        {
            "company": "Tech Corp",
            "role": "Software Engineer", 
            "duration": "2020-2023",
            "responsibilities": "Developed web applications"
        },
        {
            "company": "StartupXYZ",
            "role": "Junior Developer",
            "duration": "2018-2020", 
            "responsibilities": "Built APIs"
        }
    ],
    "education": [
        {
            "degree": "Bachelor's in Computer Science",
            "institution": "University of Technology",
            "year": "2014-2018"
        }
    ]
}

jd_data = {
    "job_title": "Full Stack Developer",
    "required_skills": ["Python", "JavaScript", "SQL", "React"],
    "preferred_skills": ["Machine Learning", "Docker"],
    "required_experience": "3+ years",
    "education_requirements": ["Bachelor's degree in Computer Science or related field"]
}

def test_endpoint():
    """Test the /intervet_new endpoint"""
    
    base_url = "http://127.0.0.1:8000"
    
    print("🧪 Testing /intervet_new endpoint")
    print("=" * 50)
    
    # Test 1: Check if server is running
    try:
        response = requests.get(f"{base_url}/", timeout=5)
        print(f"✅ Server is running: {response.status_code}")
        print(f"Response: {response.json()}")
    except Exception as e:
        print(f"❌ Server not accessible: {e}")
        return False
    
    print("\n" + "=" * 50)
    
    # Test 2: Default weights (equal)
    print("\n📊 Test 1: Default Equal Weights")
    test_payload = {
        "resume_json": resume_data,
        "jd_json": jd_data
        # No weights specified - should use defaults
    }
    
    try:
        response = requests.post(
            f"{base_url}/intervet_new",
            json=test_payload,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Success! Status: {response.status_code}")
            print(f"Final Score: {result['total_score']}/10")
            print(f"Fit Category: {result['fit_category']}")
            print(f"Credits Used: {result['credits_used']}")
            print(f"Summary: {result['summary']}")
            
            # Show detailed breakdown
            print("\nDetailed Breakdown:")
            for category, details in result['detailed_breakdown'].items():
                print(f"  {category}: {details['raw_score']}/10 (credit: {details['credit']}, weighted: {details['weighted_score']:.2f})")
                print(f"    Rationale: {details['rationale']}")
        else:
            print(f"❌ Failed! Status: {response.status_code}")
            print(f"Error: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Request failed: {e}")
        return False
    
    print("\n" + "=" * 50)
    
    # Test 3: Skills-heavy weights
    print("\n📊 Test 2: Skills-Heavy Configuration")
    test_payload_skills_heavy = {
        "resume_json": resume_data,
        "jd_json": jd_data,
        "weights": {
            "skills": 5.0,
            "experience": 2.0,
            "education": 1.0,
            "certifications": 1.0,
            "location": 0.5,
            "reliability": 1.0
        }
    }
    
    try:
        response = requests.post(
            f"{base_url}/intervet_new",
            json=test_payload_skills_heavy,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Success! Status: {response.status_code}")
            print(f"Final Score: {result['total_score']}/10")
            print(f"Fit Category: {result['fit_category']}")
            print(f"Credits Used: {result['credits_used']}")
            print(f"Total Credits: {result['total_credits']}")
        else:
            print(f"❌ Failed! Status: {response.status_code}")
            print(f"Error: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Request failed: {e}")
        return False
    
    print("\n" + "=" * 50)
    
    # Test 4: Experience-heavy weights
    print("\n📊 Test 3: Experience-Heavy Configuration")
    test_payload_exp_heavy = {
        "resume_json": resume_data,
        "jd_json": jd_data,
        "weights": {
            "skills": 2.0,
            "experience": 5.0,
            "education": 1.0,
            "certifications": 1.0,
            "location": 1.0,
            "reliability": 3.0
        }
    }
    
    try:
        response = requests.post(
            f"{base_url}/intervet_new",
            json=test_payload_exp_heavy,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Success! Status: {response.status_code}")
            print(f"Final Score: {result['total_score']}/10")
            print(f"Fit Category: {result['fit_category']}")
            print(f"Credits Used: {result['credits_used']}")
            print(f"Total Credits: {result['total_credits']}")
        else:
            print(f"❌ Failed! Status: {response.status_code}")
            print(f"Error: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Request failed: {e}")
        return False
    
    print("\n✅ All tests completed successfully!")
    print("\n📋 Key Features Verified:")
    print("   ✅ CGPA-style scoring system (0-10 scale)")
    print("   ✅ Credit-based weight configuration")
    print("   ✅ Transparent calculation logging")
    print("   ✅ Comprehensive score breakdown")
    print("   ✅ Skills matching with variations")
    print("   ✅ Flexible weight configurations")
    print("   ✅ API endpoint working correctly")
    
    return True

if __name__ == "__main__":
    # Wait a moment for server to start
    print("Waiting 3 seconds for server to start...")
    time.sleep(3)
    
    success = test_endpoint()
    if success:
        print("\n🎉 All tests passed! The /intervet_new endpoint is working correctly.")
    else:
        print("\n❌ Some tests failed. Check the server logs for details.")
