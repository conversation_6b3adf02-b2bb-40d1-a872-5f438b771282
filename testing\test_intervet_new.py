#!/usr/bin/env python3
"""
Test script for the new /intervet_new endpoint with CGPA-style scoring.
Tests different weight configurations and validates the scoring system.
"""

import requests
import json
import os
import time
import sys

# Configuration
API_BASE_URL = "http://localhost:8000"
TEST_RESUME_PATH = "../resumes for testing/Resume-Raman <PERSON>ch.pdf"
TEST_JD_PATH = "../jds/Software Engineer JD.pdf"

def test_resume_parsing():
    """Test resume parsing to get resume_json."""
    print("🔍 Step 1: Testing resume parsing...")
    
    if not os.path.exists(TEST_RESUME_PATH):
        print(f"❌ Resume file not found: {TEST_RESUME_PATH}")
        return None
    
    with open(TEST_RESUME_PATH, 'rb') as f:
        files = {'file': (os.path.basename(TEST_RESUME_PATH), f, 'application/pdf')}
        response = requests.post(f"{API_BASE_URL}/hybrid_resume", files=files, timeout=120)
    
    if response.status_code != 200:
        print(f"❌ Resume parsing failed: {response.text}")
        return None
    
    resume_data = response.json()
    print(f"✅ Resume parsed successfully")
    print(f"📊 Candidate: {resume_data.get('name', 'Unknown')}")
    print(f"📊 Skills count: {len(resume_data.get('skills', {})) if isinstance(resume_data.get('skills'), dict) else len(resume_data.get('skills', []))}")
    print(f"📊 Experience entries: {len(resume_data.get('experience', []))}")
    
    return resume_data

def test_jd_parsing():
    """Test JD parsing to get jd_json."""
    print("\n🔍 Step 2: Testing JD parsing...")
    
    if not os.path.exists(TEST_JD_PATH):
        print(f"❌ JD file not found: {TEST_JD_PATH}")
        return None
    
    with open(TEST_JD_PATH, 'rb') as f:
        files = {'file': (os.path.basename(TEST_JD_PATH), f, 'application/pdf')}
        response = requests.post(f"{API_BASE_URL}/jd_parser", files=files, timeout=120)
    
    if response.status_code != 200:
        print(f"❌ JD parsing failed: {response.text}")
        return None
    
    jd_data = response.json()
    print(f"✅ JD parsed successfully")
    print(f"📊 Job Title: {jd_data.get('job_title', 'Unknown')}")
    print(f"📊 Required skills: {len(jd_data.get('required_skills', []))}")
    print(f"📊 Education requirements: {len(jd_data.get('education_requirements', []))}")
    
    return jd_data

def test_intervet_new_configuration(resume_data, jd_data, weights_config, test_name):
    """Test a specific weight configuration with the new endpoint."""
    
    print(f"\n🧪 Testing: {test_name}")
    print(f"📊 Weights: {weights_config}")
    
    payload = {
        "resume_json": resume_data,
        "jd_json": jd_data,
        "weights": weights_config
    }
    
    start_time = time.time()
    response = requests.post(
        f"{API_BASE_URL}/intervet_new", 
        json=payload, 
        timeout=120,
        headers={"Content-Type": "application/json"}
    )
    end_time = time.time()
    
    print(f"⏱️  Request completed in {end_time - start_time:.2f} seconds")
    print(f"📊 Response status: {response.status_code}")
    
    if response.status_code != 200:
        print(f"❌ Request failed: {response.text}")
        return False
    
    result = response.json()
    
    # Validate response structure
    required_fields = ["total_score", "fit_category", "summary", "detailed_breakdown", "calculation_log", "credits_used", "total_credits"]
    for field in required_fields:
        if field not in result:
            print(f"❌ Missing required field: {field}")
            return False
    
    # Validate score range
    total_score = result["total_score"]
    if not (0 <= total_score <= 10):
        print(f"❌ Invalid total score: {total_score} (should be 0-10)")
        return False
    
    # Validate detailed breakdown
    breakdown = result["detailed_breakdown"]
    expected_categories = ["skills", "experience", "education", "certifications", "location", "reliability"]
    for category in expected_categories:
        if category not in breakdown:
            print(f"❌ Missing category in breakdown: {category}")
            return False
        
        cat_data = breakdown[category]
        if not all(key in cat_data for key in ["raw_score", "credit", "weighted_score", "rationale"]):
            print(f"❌ Missing data in category {category}")
            return False
        
        # Validate raw score range
        raw_score = cat_data["raw_score"]
        if not (0 <= raw_score <= 10):
            print(f"❌ Invalid raw score for {category}: {raw_score}")
            return False
    
    # Display results
    print(f"✅ Test passed!")
    print(f"📊 Final Score: {total_score:.2f}/10")
    print(f"📊 Fit Category: {result['fit_category']}")
    print(f"📊 Total Credits: {result['total_credits']:.1f}")
    
    # Show breakdown summary
    print(f"📊 Score Breakdown:")
    for category, data in breakdown.items():
        print(f"   {category.title()}: {data['raw_score']:.1f}/10 × {data['credit']:.1f} = {data['weighted_score']:.2f}")
    
    # Show calculation transparency
    print(f"📊 Calculation Log Lines: {len(result['calculation_log'])}")
    
    return True

def main():
    """Main test function."""
    print("🚀 Starting /intervet_new endpoint tests")
    print(f"🌐 API Base URL: {API_BASE_URL}")
    
    # Step 1: Parse resume
    resume_data = test_resume_parsing()
    if not resume_data:
        print("❌ Cannot proceed without resume data")
        return False
    
    # Step 2: Parse JD
    jd_data = test_jd_parsing()
    if not jd_data:
        print("❌ Cannot proceed without JD data")
        return False
    
    # Step 3: Test different weight configurations
    test_configs = [
        {
            "name": "Equal Weights (Default)",
            "weights": None  # Test default behavior
        },
        {
            "name": "Skills-Heavy Configuration",
            "weights": {
                "skills": 5.0,
                "experience": 2.0,
                "education": 1.0,
                "certifications": 1.0,
                "location": 0.5,
                "reliability": 1.0
            }
        },
        {
            "name": "Experience-Focused Configuration",
            "weights": {
                "skills": 3.0,
                "experience": 5.0,
                "education": 2.0,
                "certifications": 1.0,
                "location": 1.0,
                "reliability": 3.0
            }
        },
        {
            "name": "Education-Priority Configuration",
            "weights": {
                "skills": 2.0,
                "experience": 2.0,
                "education": 5.0,
                "certifications": 2.0,
                "location": 1.0,
                "reliability": 1.0
            }
        },
        {
            "name": "Balanced Professional Configuration",
            "weights": {
                "skills": 4.0,
                "experience": 4.0,
                "education": 2.0,
                "certifications": 2.0,
                "location": 1.0,
                "reliability": 3.0
            }
        }
    ]
    
    results = []
    
    for config in test_configs:
        success = test_intervet_new_configuration(
            resume_data, 
            jd_data, 
            config["weights"], 
            config["name"]
        )
        results.append(success)
    
    # Test edge cases
    print(f"\n🧪 Testing Edge Cases")
    
    # Test with all zero weights (should use equal distribution)
    print(f"\n🔍 Testing all-zero weights...")
    zero_weights = {
        "skills": 0.0,
        "experience": 0.0,
        "education": 0.0,
        "certifications": 0.0,
        "location": 0.0,
        "reliability": 0.0
    }
    zero_success = test_intervet_new_configuration(
        resume_data, 
        jd_data, 
        zero_weights, 
        "All Zero Weights"
    )
    results.append(zero_success)
    
    # Summary
    passed_tests = sum(results)
    total_tests = len(results)
    
    print(f"\n📊 Test Summary:")
    print(f"   Passed: {passed_tests}/{total_tests}")
    print(f"   Success Rate: {(passed_tests/total_tests)*100:.1f}%")
    
    if passed_tests == total_tests:
        print("\n🎉 All tests passed! The /intervet_new endpoint is working correctly.")
        print("\n📋 Features Verified:")
        print("   ✅ CGPA-style scoring system (0-10 scale)")
        print("   ✅ Credit-based weight configuration")
        print("   ✅ Transparent calculation logging")
        print("   ✅ Comprehensive score breakdown")
        print("   ✅ Default weight handling")
        print("   ✅ Edge case handling (zero weights)")
        print("   ✅ Response structure validation")
        print("   ✅ Score range validation")
        return True
    else:
        print(f"\n❌ {total_tests - passed_tests} tests failed. Check the error messages above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
