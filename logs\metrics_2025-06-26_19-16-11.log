{"event": "session_start", "session_id": "05a4176a-b2ad-460a-ba32-741e7dfa1e9e", "timestamp": "2025-06-26T19:16:11.881187", "message": "New API session started"}
{"event": "request_start", "session_id": "05a4176a-b2ad-460a-ba32-741e7dfa1e9e", "request_id": "767a0b26-a29e-4c78-8a91-ddcace3716a3", "endpoint": "/", "timestamp": "2025-06-26T19:16:18.449409", "message": "Request started for endpoint: /"}
{"event": "request_complete", "session_id": "05a4176a-b2ad-460a-ba32-741e7dfa1e9e", "request_id": "767a0b26-a29e-4c78-8a91-ddcace3716a3", "endpoint": "/", "timestamp": "2025-06-26T19:16:18.451410", "total_time_seconds": 0.002001047134399414, "status_code": 200, "message": "Request completed in 0.0020s with status 200"}
{"event": "request_start", "session_id": "05a4176a-b2ad-460a-ba32-741e7dfa1e9e", "request_id": "5168be58-f376-4857-b151-ec3eceb1e023", "endpoint": "/docs", "timestamp": "2025-06-26T19:16:21.186311", "message": "Request started for endpoint: /docs"}
{"event": "request_complete", "session_id": "05a4176a-b2ad-460a-ba32-741e7dfa1e9e", "request_id": "5168be58-f376-4857-b151-ec3eceb1e023", "endpoint": "/docs", "timestamp": "2025-06-26T19:16:21.186311", "total_time_seconds": 0.0, "status_code": 200, "message": "Request completed in 0.0000s with status 200"}
{"event": "request_start", "session_id": "05a4176a-b2ad-460a-ba32-741e7dfa1e9e", "request_id": "7238a599-35fe-4142-98a5-0caa64f01cbc", "endpoint": "/openapi.json", "timestamp": "2025-06-26T19:16:21.275420", "message": "Request started for endpoint: /openapi.json"}
{"event": "request_complete", "session_id": "05a4176a-b2ad-460a-ba32-741e7dfa1e9e", "request_id": "7238a599-35fe-4142-98a5-0caa64f01cbc", "endpoint": "/openapi.json", "timestamp": "2025-06-26T19:16:21.290917", "total_time_seconds": 0.015496253967285156, "status_code": 200, "message": "Request completed in 0.0155s with status 200"}
{"event": "request_start", "session_id": "05a4176a-b2ad-460a-ba32-741e7dfa1e9e", "request_id": "4633d4d0-4dd2-483b-b660-cc443b9141c3", "endpoint": "/hybrid_resume", "timestamp": "2025-06-26T19:16:30.131648", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "05a4176a-b2ad-460a-ba32-741e7dfa1e9e", "request_id": "4633d4d0-4dd2-483b-b660-cc443b9141c3", "endpoint": "/hybrid_resume", "timestamp": "2025-06-26T19:16:30.210634", "file_type": "docx", "message": "Custom metric: file_type=docx"}
{"event": "custom_metric", "session_id": "05a4176a-b2ad-460a-ba32-741e7dfa1e9e", "request_id": "4633d4d0-4dd2-483b-b660-cc443b9141c3", "endpoint": "/hybrid_resume", "timestamp": "2025-06-26T19:16:30.210634", "file_size_bytes": 7383, "message": "Custom metric: file_size_bytes=7383"}
{"event": "custom_metric", "session_id": "05a4176a-b2ad-460a-ba32-741e7dfa1e9e", "request_id": "4633d4d0-4dd2-483b-b660-cc443b9141c3", "endpoint": "/hybrid_resume", "timestamp": "2025-06-26T19:16:30.210634", "extraction_method": "docx_text", "message": "Custom metric: extraction_method=docx_text"}
{"event": "custom_metric", "session_id": "05a4176a-b2ad-460a-ba32-741e7dfa1e9e", "request_id": "4633d4d0-4dd2-483b-b660-cc443b9141c3", "endpoint": "/hybrid_resume", "timestamp": "2025-06-26T19:16:30.211143", "extracted_text_length": 1123, "message": "Custom metric: extracted_text_length=1123"}
{"event": "custom_metric", "session_id": "05a4176a-b2ad-460a-ba32-741e7dfa1e9e", "request_id": "4633d4d0-4dd2-483b-b660-cc443b9141c3", "endpoint": "/hybrid_resume", "timestamp": "2025-06-26T19:16:30.211143", "file_processing_time": 0.02529740333557129, "message": "Custom metric: file_processing_time=0.02529740333557129"}
{"event": "request_complete", "session_id": "05a4176a-b2ad-460a-ba32-741e7dfa1e9e", "request_id": "4633d4d0-4dd2-483b-b660-cc443b9141c3", "endpoint": "/hybrid_resume", "timestamp": "2025-06-26T19:16:41.569956", "total_time_seconds": 11.438308238983154, "status_code": 200, "message": "Request completed in 11.4383s with status 200"}
{"event": "session_end", "session_id": "05a4176a-b2ad-460a-ba32-741e7dfa1e9e", "timestamp": "2025-06-26T19:18:57.718156", "message": "API session ended"}
