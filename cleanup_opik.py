#!/usr/bin/env python3
"""
Script to clean up all Opik/Comet tracking code from main.py
"""

import re

def clean_opik_code():
    """Remove all Opik-related code from main.py"""
    
    # Read the file
    with open('main.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Remove @track decorators
    content = re.sub(r'@track\([^)]*\)\n', '', content)
    
    # Remove @safe_opik_track decorators
    content = re.sub(r'@safe_opik_track\([^)]*\)\n', '', content)
    
    # Remove safe_opik_update_trace calls (multiline)
    content = re.sub(r'safe_opik_update_trace\([^)]*\)', '', content, flags=re.DOTALL)
    
    # Remove safe_opik_update_span calls (multiline)
    content = re.sub(r'safe_opik_update_span\([^)]*\)', '', content, flags=re.DOTALL)
    
    # Remove opik_context.update_current_trace calls (multiline)
    content = re.sub(r'opik_context\.update_current_trace\([^)]*\)', '', content, flags=re.DOTALL)
    
    # Remove lines that contain only Opik-related comments
    lines = content.split('\n')
    cleaned_lines = []
    
    for line in lines:
        # Skip lines that are only Opik-related comments or calls
        if any(keyword in line for keyword in [
            '# Update Opik', 
            '# Add feedback scores',
            'ollama_client_tracked',
            'opik_enabled',
            'opik_metadata',
            'response_quality_score = min(1.0, len(response_text) / 100)',
            'if ollama_client_tracked and opik_enabled:',
            'chat_completion = ollama_client_tracked.chat.completions.create(',
            'except Exception as e:',
            'logger.debug(f"Opik'
        ]):
            # Skip these lines
            continue
        
        # Clean up any remaining references
        line = line.replace('ollama_client_tracked', 'ollama_client')
        line = line.replace('if opik_enabled:', 'if False:')  # Disable opik blocks
        
        cleaned_lines.append(line)
    
    # Join lines back
    content = '\n'.join(cleaned_lines)
    
    # Remove empty try-except blocks that might be left
    content = re.sub(r'try:\s*\n\s*except[^:]*:\s*\n\s*pass\s*\n', '', content, flags=re.MULTILINE)
    
    # Remove multiple consecutive empty lines
    content = re.sub(r'\n\s*\n\s*\n', '\n\n', content)
    
    # Write back to file
    with open('main.py', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ Cleaned up Opik/Comet tracking code from main.py")

if __name__ == "__main__":
    clean_opik_code()
