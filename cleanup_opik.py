#!/usr/bin/env python3
"""
Comprehensive script to clean up all Opik/Comet tracking code from main.py and fix syntax errors
"""

import re

def clean_opik_code():
    """Remove all Opik-related code from main.py and fix syntax errors"""

    # Read the file
    with open('main.py', 'r', encoding='utf-8') as f:
        content = f.read()

    # Fix specific syntax errors first

    # Fix incomplete try blocks by adding except clauses
    content = re.sub(r'try:\s*\n(\s+[^#\n][^\n]*\n(?:\s+[^\n]*\n)*?)(?=\s*(?:def |class |@|$))',
                     r'try:\n\1    except Exception as e:\n        logger.error(f"Error: {e}")\n', content, flags=re.MULTILINE)

    # Fix broken metadata dictionaries and function calls
    content = re.sub(r',\s*"model":', r'"model":', content)
    content = re.sub(r',\s*"job_title":', r'"job_title":', content)
    content = re.sub(r',\s*"convert_skills_to_dict":', r'"convert_skills_to_dict":', content)
    content = re.sub(r',\s*"parsing_method":', r'"parsing_method":', content)
    content = re.sub(r',\s*"processing_time":', r'"processing_time":', content)
    content = re.sub(r',\s*"success":', r'"success":', content)

    # Fix broken dictionary structures
    content = re.sub(r'}\s*chars"}', r'} chars"}', content)
    content = re.sub(r'}\s*"}', r'}"}', content)

    # Remove orphaned closing braces and parentheses
    content = re.sub(r'^\s*}\s*$', '', content, flags=re.MULTILINE)
    content = re.sub(r'^\s*\)\s*$', '', content, flags=re.MULTILINE)

    # Fix incomplete if statements
    content = re.sub(r'if "eval_count" in result:\s*\n\s*if "prompt_eval_count" in result:\s*\n\s*if "total_duration" in result:',
                     'if "eval_count" in result:\n    pass\nif "prompt_eval_count" in result:\n    pass\nif "total_duration" in result:\n    pass', content)

    # Remove @track and @safe_opik_track decorators
    content = re.sub(r'@track\([^)]*\)\n', '', content)
    content = re.sub(r'@safe_opik_track\([^)]*\)\n', '', content)

    # Remove Opik function calls (multiline)
    content = re.sub(r'safe_opik_update_trace\([^)]*\)', '', content, flags=re.DOTALL)
    content = re.sub(r'safe_opik_update_span\([^)]*\)', '', content, flags=re.DOTALL)
    content = re.sub(r'opik_context\.update_current_trace\([^)]*\)', '', content, flags=re.DOTALL)

    # Split into lines for more precise cleaning
    lines = content.split('\n')
    cleaned_lines = []
    skip_next = False
    in_opik_block = False

    for i, line in enumerate(lines):
        if skip_next:
            skip_next = False
            continue

        # Skip Opik-related lines
        if any(keyword in line for keyword in [
            '# Update Opik',
            '# Add feedback scores',
            'ollama_client_tracked',
            'opik_enabled',
            'opik_metadata',
            'response_quality_score = min(1.0, len(response_text) / 100)',
            'if ollama_client_tracked and opik_enabled:',
            'chat_completion = ollama_client_tracked.chat.completions.create(',
            'logger.debug(f"Opik',
            'logger.warning(f"Failed to update Opik',
            'logger.warning(f"Opik',
            '# Prepare metadata for Opik',
            'if opik_enabled:',
            'opik_client',
            'track_openai'
        ]):
            continue

        # Fix specific broken lines
        if line.strip().startswith(',') and any(keyword in line for keyword in ['"model":', '"job_title":', '"convert_skills_to_dict":', '"parsing_method":']):
            line = '        ' + line.strip()[1:].strip()  # Remove leading comma and fix indentation

        # Fix undefined variables
        line = line.replace('ollama_client_tracked', 'ollama_client')
        line = line.replace('if opik_enabled:', 'if False:')

        # Fix broken exception handling
        if 'logger.warning(f"Failed to update Opik trace context: {e}")' in line:
            continue
        if 'logger.error(f"Error: {e}")' in line and 'except Exception as e:' not in lines[max(0, i-3):i]:
            continue

        # Skip lines with undefined variables
        if any(undefined in line for undefined in [
            'logger.warning(f"Could not delete {filepath}: {e}")',
            'logger.error(f"Error extracting {section_name}: {e}")',
            'logger.error(f"Error processing category: {e}")'
        ]) and 'except Exception as e:' not in lines[max(0, i-5):i]:
            continue

        cleaned_lines.append(line)

    # Join lines back
    content = '\n'.join(cleaned_lines)

    # Remove empty try-except blocks
    content = re.sub(r'try:\s*\n\s*except[^:]*:\s*\n\s*pass\s*\n', '', content, flags=re.MULTILINE)

    # Remove multiple consecutive empty lines
    content = re.sub(r'\n\s*\n\s*\n+', '\n\n', content)

    # Fix any remaining syntax issues
    content = re.sub(r'except Exception as e:\s*\n\s*raise\s*\n\s*logger\.error', 'except Exception as e:\n        logger.error', content)

    # Write back to file
    with open('main.py', 'w', encoding='utf-8') as f:
        f.write(content)

    print("✅ Comprehensive cleanup completed - removed Opik code and fixed syntax errors")

if __name__ == "__main__":
    clean_opik_code()
