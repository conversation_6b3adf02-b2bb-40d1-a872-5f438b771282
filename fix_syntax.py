#!/usr/bin/env python3
"""
Script to fix critical syntax errors in main.py
"""

import re

def fix_syntax_errors():
    """Fix critical syntax errors in main.py"""
    
    # Read the file
    with open('main.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Fix the MetricsMiddleware class structure
    content = re.sub(
        r'class MetricsMiddleware\(BaseHTTPMiddleware\):\s*\n\s*async def dispatch\(self, request: Request, call_next: Callable\) -> Response:\s*\n\s*# Extract the endpoint path\s*\n\s*path = request\.url\.path\s*\n\s*\n\s*# Create metrics for this request\s*\n\s*metrics = RequestMetrics\(endpoint=path\)\s*\n\s*\n\s*# Store metrics in request state for access in route handlers\s*\n\s*request\.state\.metrics = metrics\s*\n\s*\n\s*try:\s*\n\s*# Call the next middleware or route handler\s*\n\s*response = await call_next\(request\)\s*\n\s*except Exception as e:\s*\n\s*\n\s*# Mark request as complete with success status\s*\n\s*metrics\.mark_complete\(status_code=response\.status_code\)\s*\n\s*\n\s*return response\s*\n\s*\n\s*# Mark request as complete with error\s*\n\s*metrics\.mark_complete\(status_code=500, error=str\(e\)\)',
        '''class MetricsMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # Extract the endpoint path
        path = request.url.path
        
        # Create metrics for this request
        metrics = RequestMetrics(endpoint=path)
        
        # Store metrics in request state for access in route handlers
        request.state.metrics = metrics
        
        try:
            # Call the next middleware or route handler
            response = await call_next(request)
            
            # Mark request as complete with success status
            metrics.mark_complete(status_code=response.status_code)
            
            return response
            
        except Exception as e:
            # Mark request as complete with error
            metrics.mark_complete(status_code=500, error=str(e))
            
            # Return error response
            from fastapi.responses import JSONResponse
            return JSONResponse(
                status_code=500,
                content={"detail": "Internal server error"}
            )''',
        content,
        flags=re.DOTALL
    )
    
    # Fix broken try-except blocks
    content = re.sub(r'try:\s*\n(\s+[^#\n][^\n]*\n(?:\s+[^\n]*\n)*?)(?=\s*(?:def |class |@|$))', 
                     r'try:\n\1    except Exception as e:\n        logger.error(f"Error: {e}")\n', content, flags=re.MULTILINE)
    
    # Fix incomplete function calls and dictionaries
    content = re.sub(r'}\s*$\s*\n\s*app\.add_middleware', r'}\n\napp.add_middleware', content, flags=re.MULTILINE)
    
    # Fix broken schema examples by removing them temporarily
    content = re.sub(
        r'class Config:\s*\n\s*extra = "allow".*?schema_extra = \{.*?\}\s*\}',
        '''class Config:
        extra = "allow"  # Allow additional fields beyond the predefined schema''',
        content,
        flags=re.DOTALL
    )
    
    # Fix the lifespan function
    content = re.sub(
        r'@asynccontextmanager\s*\n\s*async def lifespan\(_: FastAPI\):.*?shutdown_metrics_logger\(\)',
        '''@asynccontextmanager
async def lifespan(_: FastAPI):
    # Startup: nothing to do
    yield
    # Shutdown: close metrics logger
    shutdown_metrics_logger()''',
        content,
        flags=re.DOTALL
    )
    
    # Add missing imports at the top
    if 'from contextlib import asynccontextmanager' not in content:
        content = content.replace(
            'from typing import Dict, List, Optional, Literal, Any, Callable, Tuple, Set',
            'from typing import Dict, List, Optional, Literal, Any, Callable, Tuple, Set\nfrom contextlib import asynccontextmanager'
        )
    
    if 'import ollama' not in content:
        content = content.replace(
            '# Removed Opik/Comet tracking functionality as requested',
            '# Removed Opik/Comet tracking functionality as requested\nimport ollama'
        )
    
    # Fix the app initialization with lifespan
    content = re.sub(
        r'app = FastAPI\(\s*title="Gemma 3:4B API",\s*description="API for interacting with Gemma 3:4B model and parsing resumes",\s*version="1\.0\.0"\s*\)',
        'app = FastAPI(\n    title="Gemma 3:4B API",\n    description="API for interacting with Gemma 3:4B model and parsing resumes",\n    version="1.0.0",\n    lifespan=lifespan\n)',
        content
    )
    
    # Write back to file
    with open('main.py', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ Fixed critical syntax errors in main.py")

if __name__ == "__main__":
    fix_syntax_errors()
