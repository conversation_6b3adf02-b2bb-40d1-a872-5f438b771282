#!/usr/bin/env python3
"""
Comprehensive script to fix all syntax errors in main.py
"""

import re

def fix_syntax_errors():
    """Fix all critical syntax errors in main.py"""

    # Read the file
    with open('main.py', 'r', encoding='utf-8') as f:
        content = f.read()

    print("🔧 Starting comprehensive syntax fixes...")

    # 1. Fix the Ollama client initialization
    content = re.sub(
        r'try:\s*\n\s*# Initialize Ollama client\s*\n\s*ollama_client = ollama\.Client\(\s*\n\s*host=\'http://localhost:11434\',\s*\n\s*timeout=120\s*\n\s*\)\s*\n\s*logger\.info\("✅ Ollama client configured"\)\s*\n\s*except Exception as e:\s*\n\s*logger\.error\(f"Error: \{e\}"\)\s*\n\s*\n\s*except Exception as e:\s*\n\s*logger\.error\(f"Failed to initialize Ollama client: \{e\}"\)\s*\n\s*ollama_client = None',
        '''try:
    # Initialize Ollama client
    ollama_client = ollama.Client(
        host='http://localhost:11434',
        timeout=120
    )
    logger.info("✅ Ollama client configured")

except Exception as e:
    logger.error(f"Failed to initialize Ollama client: {e}")
    ollama_client = None''',
        content,
        flags=re.DOTALL
    )

    # 2. Fix the MetricsMiddleware class completely
    middleware_pattern = r'class MetricsMiddleware\(BaseHTTPMiddleware\):.*?(?=\n# Add the metrics middleware)'
    middleware_replacement = '''class MetricsMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # Extract the endpoint path
        path = request.url.path

        # Create metrics for this request
        metrics = RequestMetrics(endpoint=path)

        # Store metrics in request state for access in route handlers
        request.state.metrics = metrics

        try:
            # Call the next middleware or route handler
            response = await call_next(request)

            # Mark request as complete with success status
            metrics.mark_complete(status_code=response.status_code)

            return response

        except Exception as e:
            # Mark request as complete with error
            metrics.mark_complete(status_code=500, error=str(e))

            # Return error response
            from fastapi.responses import JSONResponse
            return JSONResponse(
                status_code=500,
                content={"detail": "Internal server error"}
            )

'''
    content = re.sub(middleware_pattern, middleware_replacement, content, flags=re.DOTALL)

    # 3. Fix the lifespan function definition and placement
    # First, remove any existing broken lifespan definitions
    content = re.sub(r'@asynccontextmanager.*?async def lifespan.*?shutdown_metrics_logger\(\)', '', content, flags=re.DOTALL)
    content = re.sub(r'# Update app with lifespan.*?app\.router\.lifespan_context = lifespan', '', content, flags=re.DOTALL)

    # Add the lifespan function before the app definition
    app_pattern = r'(# Add CORS middleware\s*\napp\.add_middleware\(.*?\))'
    lifespan_and_app = '''# Register lifespan context for startup/shutdown events
from contextlib import asynccontextmanager

@asynccontextmanager
async def lifespan(_: FastAPI):
    # Startup: nothing to do
    yield
    # Shutdown: close metrics logger
    shutdown_metrics_logger()

\\1'''
    content = re.sub(app_pattern, lifespan_and_app, content, flags=re.DOTALL)

    # 4. Fix the app initialization to include lifespan
    content = re.sub(
        r'app = FastAPI\(\s*title="Gemma 3:4B API",\s*description="API for interacting with Gemma 3:4B model and parsing resumes",\s*version="1\.0\.0"\s*\)',
        '''app = FastAPI(
    title="Gemma 3:4B API",
    description="API for interacting with Gemma 3:4B model and parsing resumes",
    version="1.0.0",
    lifespan=lifespan
)''',
        content
    )

    # 5. Fix all broken schema examples by removing them
    # Remove broken Config classes with incomplete schema_extra
    content = re.sub(
        r'class Config:\s*\n\s*schema_extra = \{[^}]*$',
        'class Config:\n        pass',
        content,
        flags=re.MULTILINE
    )

    # 6. Fix broken BaseModel field definitions
    content = re.sub(r'(\w+): ([^=\n]+) = ([^=\n]+)\s*$', r'\1: \2 = \3', content, flags=re.MULTILINE)

    # 7. Fix the get_response function
    get_response_pattern = r'def get_response\(.*?\):\s*\n\s*try:\s*\n\s*logger\.info\(.*?\)\s*\n\s*except Exception as e:\s*\n\s*except Exception as e:'
    get_response_replacement = '''def get_response(prompt: str, timeout_seconds: int = 60, max_tokens: int = 1000, image_path: str = None, request_metrics: RequestMetrics = None, endpoint: str = "unknown", context: str = None, call_type: str = "main") -> str:
    try:
        logger.info(f"Sending prompt to {MODEL_NAME} with {timeout_seconds}s timeout")'''
    content = re.sub(get_response_pattern, get_response_replacement, content, flags=re.DOTALL)

    # 8. Add missing imports at the top if not present
    if 'from contextlib import asynccontextmanager' not in content:
        content = content.replace(
            'from typing import Dict, List, Optional, Literal, Any, Callable, Tuple, Set',
            'from typing import Dict, List, Optional, Literal, Any, Callable, Tuple, Set\nfrom contextlib import asynccontextmanager'
        )

    if 'import ollama' not in content:
        content = content.replace(
            '# Removed Opik/Comet tracking functionality as requested',
            '# Removed Opik/Comet tracking functionality as requested\nimport ollama'
        )

    # 9. Remove duplicate imports
    content = re.sub(r'from contextlib import asynccontextmanager\s*\nfrom contextlib import asynccontextmanager', 'from contextlib import asynccontextmanager', content)

    # 10. Clean up any remaining syntax issues
    content = re.sub(r'\n\s*\n\s*\n+', '\n\n', content)  # Remove excessive blank lines

    # Write back to file
    with open('main.py', 'w', encoding='utf-8') as f:
        f.write(content)

    print("✅ Comprehensive syntax fixes completed")

if __name__ == "__main__":
    fix_syntax_errors()
