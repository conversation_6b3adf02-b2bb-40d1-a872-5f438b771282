#!/usr/bin/env python3
"""
Minimal working version of main.py focused on the new /intervet_new endpoint
"""

import json
import logging
import os
import re
import time
from datetime import datetime
from typing import Dict, List, Optional, Any
from contextlib import asynccontextmanager

from fastapi import FastAP<PERSON>, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from starlette.middleware.base import BaseHTTPMiddleware
from pydantic import BaseModel, Field
import ollama

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Model configuration
MODEL_NAME = "gemma2:9b"

# Initialize Ollama client
try:
    ollama_client = ollama.Client(
        host='http://localhost:11434',
        timeout=120
    )
    logger.info("✅ Ollama client configured")
except Exception as e:
    logger.error(f"Failed to initialize Ollama client: {e}")
    ollama_client = None

# Pydantic models for the new endpoint
class WeightageNewConfig(BaseModel):
    """New weightage configuration for /intervet_new endpoint with flexible credit system."""
    skills: float = Field(default=1.0, ge=0.0, le=5.0, description="Credits for skills matching (0-5, default 1.0 for equal weight)")
    experience: float = Field(default=1.0, ge=0.0, le=5.0, description="Credits for experience matching (0-5, default 1.0 for equal weight)")
    education: float = Field(default=1.0, ge=0.0, le=5.0, description="Credits for education matching (0-5, default 1.0 for equal weight)")
    certifications: float = Field(default=1.0, ge=0.0, le=5.0, description="Credits for certifications (0-5, default 1.0 for equal weight)")
    location: float = Field(default=1.0, ge=0.0, le=5.0, description="Credits for location matching (0-5, default 1.0 for equal weight)")
    reliability: float = Field(default=1.0, ge=0.0, le=5.0, description="Credits for job stability/reliability (0-5, default 1.0 for equal weight)")

class IntervetNewRequest(BaseModel):
    resume_json: Dict[str, Any] = Field(..., description="Resume data in JSON format, typically obtained from the /resume endpoint")
    jd_json: Dict[str, Any] = Field(..., description="Job description data in JSON format, typically obtained from the /jd_parser endpoint")
    weights: Optional[WeightageNewConfig] = Field(default=None, description="Optional weights/credits for each scoring category (0-5 scale). If not provided, uses equal weights (1.0 for all categories)")

# Simple metrics tracking
class RequestMetrics:
    def __init__(self, endpoint: str):
        self.endpoint = endpoint
        self.start_time = time.time()
        self.status_code = None
        
    def mark_complete(self, status_code: int, error: str = None):
        self.status_code = status_code
        processing_time = time.time() - self.start_time
        logger.info(f"Request to {self.endpoint} completed in {processing_time:.2f}s with status {status_code}")

# Simple middleware for metrics
class MetricsMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        metrics = RequestMetrics(endpoint=request.url.path)
        request.state.metrics = metrics
        
        try:
            response = await call_next(request)
            metrics.mark_complete(status_code=response.status_code)
            return response
        except Exception as e:
            metrics.mark_complete(status_code=500, error=str(e))
            raise

# Lifespan context
@asynccontextmanager
async def lifespan(_: FastAPI):
    # Startup: nothing to do
    yield
    # Shutdown: nothing to do

# Create FastAPI app
app = FastAPI(
    title="Gemma 3:4B API - Minimal",
    description="Minimal API for testing the new /intervet_new endpoint",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add metrics middleware
app.add_middleware(MetricsMiddleware)

# Helper functions for the new endpoint
def is_skill_match(skill1: str, skill2: str) -> bool:
    """Check if two skills match (case-insensitive, handles variations)."""
    skill1_clean = skill1.lower().strip()
    skill2_clean = skill2.lower().strip()
    
    # Direct match
    if skill1_clean == skill2_clean:
        return True
    
    # Check if one is contained in the other
    if skill1_clean in skill2_clean or skill2_clean in skill1_clean:
        return True
    
    # Handle common variations
    variations = {
        'javascript': ['js', 'node.js', 'nodejs'],
        'python': ['py'],
        'machine learning': ['ml', 'ai', 'artificial intelligence'],
        'sql': ['mysql', 'postgresql', 'sqlite'],
        'react': ['reactjs', 'react.js'],
        'angular': ['angularjs'],
        'vue': ['vuejs', 'vue.js']
    }
    
    for main_skill, alts in variations.items():
        if (skill1_clean == main_skill and skill2_clean in alts) or \
           (skill2_clean == main_skill and skill1_clean in alts):
            return True
    
    return False

def find_matching_skills(candidate_skills: List[str], required_skills: List[str]) -> tuple:
    """Find matching skills between candidate and required skills."""
    matched = []
    missing = []
    
    for req_skill in required_skills:
        found_match = False
        for cand_skill in candidate_skills:
            if is_skill_match(cand_skill, req_skill):
                matched.append(req_skill)
                found_match = True
                break
        
        if not found_match:
            missing.append(req_skill)
    
    return matched, missing

def calculate_candidate_job_fit_new(resume_data: Dict, jd_data: Dict, weights: Optional[WeightageNewConfig] = None) -> Dict:
    """
    Calculate how well a candidate's resume matches a job description using CGPA-style credit system.
    
    This function evaluates the fit between a candidate and a job based on multiple criteria with
    credit-based scoring that normalizes to a final score out of 10:
    1. Skills matching (direct matching between resume and JD skills)
    2. Experience matching (years of experience vs requirements)
    3. Education matching (degree level and field alignment)
    4. Certifications (relevant certifications)
    5. Location matching (geographic alignment)
    6. Reliability (job stability based on tenure)
    
    The scoring works like CGPA calculation:
    - Each category gets a raw score (0-10) based on matching quality
    - Raw scores are multiplied by their respective credits/weights
    - Final score = Sum(score * credit) / Sum(credits) to normalize to 0-10 scale
    
    Returns a comprehensive evaluation with transparent calculation logging.
    """
    logger.info("Starting new CGPA-style candidate-job fit calculation")
    
    # Set up default weights if not provided (equal credits for all categories)
    if weights is None:
        weights = WeightageNewConfig()
    
    # Extract credit values
    credits = {
        "skills": weights.skills,
        "experience": weights.experience,
        "education": weights.education,
        "certifications": weights.certifications,
        "location": weights.location,
        "reliability": weights.reliability
    }
    
    # Calculate total credits for normalization
    total_credits = sum(credits.values())
    
    # If all credits are 0, use equal distribution
    if total_credits == 0:
        credits = {k: 1.0 for k in credits.keys()}
        total_credits = sum(credits.values())
    
    # Initialize scoring data
    raw_scores = {}
    weighted_scores = {}
    rationale = {}
    calculation_log = []
    
    # Log input configuration
    calculation_log.append(f"=== CGPA-Style Scoring Configuration ===")
    calculation_log.append(f"Credits: {credits}")
    calculation_log.append(f"Total Credits: {total_credits}")
    calculation_log.append(f"Candidate: {resume_data.get('name', 'Unknown')}")
    calculation_log.append(f"Position: {jd_data.get('job_title', 'Unknown')}")
    calculation_log.append("")
    
    # 1. SKILLS MATCHING (0-10 raw score)
    calculation_log.append("=== 1. SKILLS MATCHING ===")
    
    # Extract skills from resume
    resume_skills_list = []
    if "skills" in resume_data:
        if isinstance(resume_data["skills"], list):
            resume_skills_list = resume_data["skills"]
        elif isinstance(resume_data["skills"], dict):
            resume_skills_list = list(resume_data["skills"].keys())
    
    # Extract required and preferred skills from JD
    jd_required_skills_list = []
    jd_preferred_skills_list = []
    
    if "required_skills" in jd_data and isinstance(jd_data["required_skills"], list):
        jd_required_skills_list = jd_data["required_skills"]
    
    if "preferred_skills" in jd_data and isinstance(jd_data["preferred_skills"], list):
        jd_preferred_skills_list = jd_data["preferred_skills"]
    
    calculation_log.append(f"Resume Skills: {resume_skills_list}")
    calculation_log.append(f"JD Required Skills: {jd_required_skills_list}")
    calculation_log.append(f"JD Preferred Skills: {jd_preferred_skills_list}")
    
    # Calculate skills matching
    if jd_required_skills_list:
        matched_required, missing_required = find_matching_skills(resume_skills_list, jd_required_skills_list)
        required_match_ratio = len(matched_required) / len(jd_required_skills_list)
        
        # Bonus for preferred skills
        preferred_bonus = 0
        matched_preferred = []
        if jd_preferred_skills_list:
            matched_preferred, _ = find_matching_skills(resume_skills_list, jd_preferred_skills_list)
            preferred_match_ratio = len(matched_preferred) / len(jd_preferred_skills_list)
            preferred_bonus = preferred_match_ratio * 2  # Max 2 points bonus for preferred skills
        
        # Calculate raw skills score (0-10)
        # Base score from required skills (0-8) + preferred skills bonus (0-2)
        skills_raw_score = min(10, (required_match_ratio * 8) + preferred_bonus)
        
        calculation_log.append(f"Required Skills Matched: {len(matched_required)}/{len(jd_required_skills_list)} = {required_match_ratio:.2f}")
        calculation_log.append(f"Matched Required: {matched_required}")
        calculation_log.append(f"Missing Required: {missing_required}")
        if jd_preferred_skills_list:
            calculation_log.append(f"Preferred Skills Matched: {len(matched_preferred)}/{len(jd_preferred_skills_list)} = {preferred_match_ratio:.2f}")
            calculation_log.append(f"Matched Preferred: {matched_preferred}")
            calculation_log.append(f"Preferred Bonus: {preferred_bonus:.2f}")
        calculation_log.append(f"Skills Raw Score: {skills_raw_score:.2f}/10")
        
        rationale["skills"] = f"Matched {len(matched_required)}/{len(jd_required_skills_list)} required skills"
        if matched_preferred:
            rationale["skills"] += f" and {len(matched_preferred)}/{len(jd_preferred_skills_list)} preferred skills"
    else:
        skills_raw_score = 5.0  # Neutral score if no requirements specified
        calculation_log.append("No required skills specified - using neutral score")
        rationale["skills"] = "No required skills specified in job description"
    
    raw_scores["skills"] = skills_raw_score
    weighted_scores["skills"] = skills_raw_score * credits["skills"]
    calculation_log.append(f"Skills Weighted Score: {skills_raw_score:.2f} × {credits['skills']:.1f} = {weighted_scores['skills']:.2f}")
    calculation_log.append("")
    
    # For brevity in this minimal version, set other categories to neutral scores (5.0)
    # In a full implementation, these would be calculated properly
    for category in ["experience", "education", "certifications", "location", "reliability"]:
        raw_scores[category] = 5.0  # Neutral score
        weighted_scores[category] = 5.0 * credits[category]
        rationale[category] = f"Neutral score (full implementation pending)"
        calculation_log.append(f"=== {category.upper()} ===")
        calculation_log.append(f"Using neutral score: 5.0/10")
        calculation_log.append(f"{category.title()} Weighted Score: 5.0 × {credits[category]:.1f} = {weighted_scores[category]:.2f}")
        calculation_log.append("")
    
    # FINAL CGPA-STYLE CALCULATION
    calculation_log.append("=== FINAL CGPA-STYLE CALCULATION ===")
    
    # Calculate total weighted score and final CGPA score
    total_weighted_score = sum(weighted_scores.values())
    final_cgpa_score = total_weighted_score / total_credits
    
    calculation_log.append(f"Total Weighted Score: {total_weighted_score:.2f}")
    calculation_log.append(f"Total Credits: {total_credits:.1f}")
    calculation_log.append(f"Final CGPA Score: {total_weighted_score:.2f} ÷ {total_credits:.1f} = {final_cgpa_score:.2f}/10")
    
    # Determine fit category based on CGPA score
    if final_cgpa_score >= 8.5:
        fit_category = "Excellent Match"
    elif final_cgpa_score >= 7.0:
        fit_category = "Strong Match"
    elif final_cgpa_score >= 5.5:
        fit_category = "Good Match"
    elif final_cgpa_score >= 4.0:
        fit_category = "Moderate Match"
    else:
        fit_category = "Weak Match"
    
    calculation_log.append(f"Fit Category: {fit_category}")
    
    # Create detailed breakdown for transparency
    breakdown = {}
    for category in raw_scores.keys():
        breakdown[category] = {
            "raw_score": round(raw_scores[category], 2),
            "credit": credits[category],
            "weighted_score": round(weighted_scores[category], 2),
            "rationale": rationale[category]
        }
    
    # Create summary
    summary = f"The candidate is a {fit_category.lower()} for this position with a CGPA-style score of {final_cgpa_score:.2f}/10. "
    
    # Add key strengths and weaknesses
    strengths = []
    weaknesses = []
    
    for category, score in raw_scores.items():
        if score >= 8.0:
            strengths.append(category.replace("_", " ").title())
        elif score <= 3.0:
            weaknesses.append(category.replace("_", " ").title())
    
    if strengths:
        summary += f"Key strengths: {', '.join(strengths)}. "
    
    if weaknesses:
        summary += f"Areas for improvement: {', '.join(weaknesses)}."
    
    # Return comprehensive result
    return {
        "total_score": round(final_cgpa_score, 2),
        "fit_category": fit_category,
        "summary": summary,
        "detailed_breakdown": breakdown,
        "calculation_log": calculation_log,
        "credits_used": credits,
        "total_credits": total_credits
    }

# API Endpoints
@app.get("/")
async def root():
    return {
        "message": "Welcome to the Gemma3 API - Minimal Version",
        "endpoints": [
            "/intervet_new - New CGPA-style candidate-job fit evaluation"
        ],
        "status": "running"
    }

@app.post("/intervet_new", response_model=Dict, summary="New CGPA-style candidate-job fit evaluation", description="Evaluate candidate-job fit using CGPA-style credit-based scoring with transparent calculation logging")
async def evaluate_candidate_job_fit_new_endpoint(request: IntervetNewRequest):
    """
    Evaluate how well a candidate's resume matches a job description using a new CGPA-style scoring system.
    
    This endpoint uses a credit-based scoring system similar to CGPA calculation:
    - Each scoring category (skills, experience, education, etc.) gets credits/weights (0-5 scale)
    - Raw scores (0-10) are calculated for each category based on matching quality
    - Final score = Sum(raw_score × credit) / Sum(credits) to normalize to 0-10 scale
    - Provides transparent calculation logging and detailed breakdown
    
    The system is flexible and allows dynamic weight configuration based on job requirements.
    """
    try:
        logger.info("Processing /intervet_new request")
        
        # Validate input data
        if not request.resume_json:
            raise HTTPException(status_code=400, detail="Resume JSON data is required")
        
        if not request.jd_json:
            raise HTTPException(status_code=400, detail="Job description JSON data is required")
        
        # Calculate the fit using the new CGPA-style system
        result = calculate_candidate_job_fit_new(
            resume_data=request.resume_json,
            jd_data=request.jd_json,
            weights=request.weights
        )
        
        logger.info(f"✅ New CGPA-style evaluation completed: {result['total_score']}/10 ({result['fit_category']})")
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in /intervet_new endpoint: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

if __name__ == "__main__":
    import uvicorn
    print("🚀 Starting server on http://127.0.0.1:8000")
    uvicorn.run(app, host="127.0.0.1", port=8000)
