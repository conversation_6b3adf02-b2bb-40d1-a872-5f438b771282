#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to fix all the broken Pydantic model definitions
"""

import re

def fix_models():
    """Fix all broken Pydantic model definitions"""
    
    # Read the file
    with open('main.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    print("🔧 Fixing broken Pydantic models...")
    
    # 1. Fix the lifespan function placement - move it before app definition
    # Remove existing broken lifespan definitions
    content = re.sub(r'# Register lifespan context.*?shutdown_metrics_logger\(\)', '', content, flags=re.DOTALL)
    
    # Find the app definition and add lifespan before it
    app_pattern = r'(app = FastAPI\(.*?\))'
    lifespan_code = '''# Register lifespan context for startup/shutdown events
from contextlib import asynccontextmanager

@asynccontextmanager
async def lifespan(_: FastAPI):
    # Startup: nothing to do
    yield
    # Shutdown: close metrics logger
    shutdown_metrics_logger()

'''
    content = re.sub(app_pattern, lifespan_code + r'\1', content, flags=re.DOTALL)
    
    # 2. Fix the app definition to include lifespan
    content = re.sub(
        r'app = FastAPI\(\s*title="Gemma 3:4B API",\s*description="API for interacting with Gemma 3:4B model and parsing resumes",\s*version="1\.0\.0",\s*lifespan=lifespan\s*\)',
        '''app = FastAPI(
    title="Gemma 3:4B API",
    description="API for interacting with Gemma 3:4B model and parsing resumes",
    version="1.0.0",
    lifespan=lifespan
)''',
        content
    )
    
    # 3. Remove all broken schema examples completely
    # Find and replace all broken Config classes
    
    # Fix IntervetNewRequest class
    intervet_new_pattern = r'class IntervetNewRequest\(BaseModel\):.*?(?=class [A-Z]|\ndef |\n@|\napp\.)'
    intervet_new_replacement = '''class IntervetNewRequest(BaseModel):
    resume_json: Dict[str, Any] = Field(..., description="Resume data in JSON format, typically obtained from the /resume endpoint")
    jd_json: Dict[str, Any] = Field(..., description="Job description data in JSON format, typically obtained from the /jd_parser endpoint")
    weights: Optional[WeightageNewConfig] = Field(default=None, description="Optional weights/credits for each scoring category (0-5 scale). If not provided, uses equal weights (1.0 for all categories)")

'''
    content = re.sub(intervet_new_pattern, intervet_new_replacement, content, flags=re.DOTALL)
    
    # Fix InterfixResponse class
    interfix_response_pattern = r'class InterfixResponse\(BaseModel\):.*?(?=class [A-Z]|\ndef |\n@|\napp\.)'
    interfix_response_replacement = '''class InterfixResponse(BaseModel):
    offer_in_hand: Optional[float] = None
    notice_period: Optional[str] = None
    expected_salary: Optional[float] = None
    reason_to_switch: Optional[str] = None
    preferred_time_for_interview: Optional[str] = None
    preferred_date_for_interview: Optional[str] = None

'''
    content = re.sub(interfix_response_pattern, interfix_response_replacement, content, flags=re.DOTALL)
    
    # Fix InterfixRequest class
    interfix_request_pattern = r'class InterfixRequest\(BaseModel\):.*?(?=class [A-Z]|\ndef |\n@|\napp\.)'
    interfix_request_replacement = '''class InterfixRequest(BaseModel):
    summary: str = Field(..., description="Summary or transcript of VAPI response (AI call agent)")

'''
    content = re.sub(interfix_request_pattern, interfix_request_replacement, content, flags=re.DOTALL)
    
    # Fix SectionExtractionResponse class
    section_response_pattern = r'class SectionExtractionResponse\(BaseModel\):.*?(?=def |class [A-Z]|\n@|\napp\.)'
    section_response_replacement = '''class SectionExtractionResponse(BaseModel):
    filename: str
    extraction_method: str  # "multiple_calls" or "single_call"
    sections_extracted: Dict[str, str] = {}  # Section name -> extracted content
    extraction_stats: Dict[str, Any] = {}  # Statistics about the extraction
    confidence_scores: Dict[str, float] = {}  # Confidence per section
    overall_confidence: float = 0.0
    processing_time: float = 0.0
    errors: List[str] = []

'''
    content = re.sub(section_response_pattern, section_response_replacement, content, flags=re.DOTALL)
    
    # 4. Fix the get_response function definition
    get_response_pattern = r'def get_response\(.*?\):\s*\n\s*try:\s*\n\s*logger\.info\(.*?\)\s*\n\s*except Exception as e:\s*\n\s*except Exception as e:'
    get_response_replacement = '''def get_response(prompt: str, timeout_seconds: int = 60, max_tokens: int = 1000, image_path: str = None, request_metrics: RequestMetrics = None, endpoint: str = "unknown", context: str = None, call_type: str = "main") -> str:
    try:
        logger.info(f"Sending prompt to {MODEL_NAME} with {timeout_seconds}s timeout")
        
        # Initialize response
        response = ""
        start_time = time.time()
        
        # Create a function to process the stream with timeout tracking
        def process_stream():
            nonlocal response
            try:
                # Prepare options
                options = {"num_predict": max_tokens}
                
                # Use standard Ollama client
                if image_path:
                    # For image inputs, use the generate method with image
                    result = ollama_client.generate(
                        model=MODEL_NAME,
                        prompt=prompt,
                        images=[image_path],
                        options=options,
                        stream=False
                    )
                else:
                    # For text-only inputs
                    result = ollama_client.generate(
                        model=MODEL_NAME,
                        prompt=prompt,
                        options=options,
                        stream=False
                    )
                
                # Extract response
                response_text = result["response"]
                processing_time = time.time() - start_time
                
                logger.info(f"✅ Received response from {MODEL_NAME} in {processing_time:.2f}s")
                
                # Log the LLM call for debugging
                try:
                    log_llm_call(
                        prompt=prompt,
                        response=response_text,
                        model=MODEL_NAME,
                        processing_time=processing_time,
                        endpoint=endpoint,
                        context=context,
                        call_type=call_type,
                        success=True
                    )
                except Exception as log_error:
                    logger.debug(f"Failed to log LLM call: {log_error}")
                
                return response_text
                
            except Exception as e:
                processing_time = time.time() - start_time
                logger.error(f"Error in process_stream: {e}")
                
                # Log the failed LLM call
                try:
                    log_llm_call(
                        prompt=prompt,
                        response="",
                        model=MODEL_NAME,
                        processing_time=processing_time,
                        endpoint=endpoint,
                        context=context,
                        call_type=call_type,
                        success=False,
                        error=str(e)
                    )
                except Exception as log_error:
                    logger.debug(f"Failed to log failed LLM call: {log_error}")
                
                raise e
        
        # Execute with timeout
        import signal
        
        def timeout_handler(signum, frame):
            raise TimeoutError(f"LLM call timed out after {timeout_seconds} seconds")
        
        # Set up timeout
        signal.signal(signal.SIGALRM, timeout_handler)
        signal.alarm(timeout_seconds)
        
        try:
            response = process_stream()
            signal.alarm(0)  # Cancel the alarm
            return response
            
        except TimeoutError as e:
            logger.error(f"LLM call timed out: {e}")
            raise HTTPException(status_code=408, detail=f"Request timed out after {timeout_seconds} seconds")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating response: {e}")
        raise HTTPException(status_code=500, detail=f"Error generating response: {str(e)}")'''
    
    content = re.sub(get_response_pattern, get_response_replacement, content, flags=re.DOTALL)
    
    # 5. Clean up any remaining broken syntax
    content = re.sub(r'\n\s*\n\s*\n+', '\n\n', content)  # Remove excessive blank lines
    
    # Write back to file
    with open('main.py', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ Fixed broken Pydantic models and function definitions")

if __name__ == "__main__":
    fix_models()
